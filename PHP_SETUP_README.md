# راهنمای نصب دیتابیس با PHP

## فایل‌های PHP ایجاد شده

### 1. `database_setup.php` (فایل اصلی نصب)
فایل اصلی که عملیات نصب و بروزرسانی دیتابیس را انجام می‌دهد.

### 2. `database_config.php` (فایل تنظیمات)
فایل تنظیمات که اطلاعات دیتابیس و امنیت را شامل می‌شود.

## نحوه استفاده

### مرحله 1: تنظیم کانفیگ
فایل `database_config.php` را ویرایش کنید:

```php
return [
    'database' => [
        'host' => 'localhost',           // آدرس سرور
        'username' => 'your_username',   // نام کاربری
        'password' => 'your_password',   // رمز عبور
        'database' => 'your_db_name',    // نام دیتابیس
        'charset' => 'utf8mb4'
    ],
    // ...
];
```

### مرحله 2: اجرای نصب

#### روش 1: از طریق مرورگر (پیشنهادی)
```
http://yoursite.com/database_setup.php
```

#### روش 2: از طریق Command Line
```bash
php database_setup.php
```

## ویژگی‌های فایل PHP

### ✅ امنیت
- محدودیت IP (فقط localhost به صورت پیش‌فرض)
- امکان تنظیم رمز عبور برای اجرا
- بررسی دسترسی‌های لازم

### ✅ هوشمند
- چک کردن وجود تیبل‌ها قبل از ایجاد
- چک کردن وجود ستون‌ها قبل از اضافه کردن
- مدیریت خطاها و نمایش پیام‌های مفید

### ✅ گزارش‌دهی کامل
- نمایش مراحل نصب به صورت زنده
- خلاصه عملیات انجام شده
- نمایش خطاها و هشدارها

### ✅ سازگاری
- کار با مرورگر و Command Line
- پشتیبانی از فارسی
- سازگار با MySQL/MariaDB

## تنظیمات امنیتی

### محدود کردن دسترسی به IP خاص:
```php
'allowed_ips' => ['*************', '127.0.0.1']
```

### فعال کردن رمز عبور:
```php
'require_auth' => true,
'auth_password' => 'your_secure_password'
```

سپس استفاده کنید:
```
http://yoursite.com/database_setup.php?password=your_secure_password
```

## عیب‌یابی

### خطای اتصال به دیتابیس:
1. تنظیمات `database_config.php` را بررسی کنید
2. مطمئن شوید MySQL/MariaDB در حال اجرا است
3. دسترسی‌های کاربر را چک کنید

### خطای دسترسی غیرمجاز:
1. IP خود را به `allowed_ips` اضافه کنید
2. یا `allowed_ips` را خالی بگذارید: `[]`

### خطای ایجاد تیبل:
1. مطمئن شوید کاربر دسترسی CREATE دارد
2. فضای کافی روی دیسک موجود باشد

## مثال خروجی موفق

```
🚀 شروع نصب دیتابیس Store Bot
=====================================

✅ بررسی امنیت انجام شد
✅ اتصال به MySQL برقرار شد
✅ دیتابیس 'store_bot_db' آماده است

📋 ایجاد تیبل‌ها:
-------------------
✅ ایجاد جدول apis
✅ ایجاد جدول autoorders
✅ ایجاد جدول category
✅ ایجاد جدول manual
✅ ایجاد جدول orders
✅ ایجاد جدول payment
✅ ایجاد جدول product
✅ ایجاد جدول sendall
✅ ایجاد جدول tusers
✅ ایجاد جدول user
✅ ایجاد جدول nowpayments

🔄 بروزرسانی ستون‌ها:
----------------------
✅ اضافه کردن ستون payment_type به جدول payment
✅ اضافه کردن ستون datap به جدول user

📝 اضافه کردن داده‌های اولیه:
-------------------------------
✅ اضافه کردن داده اولیه به جدول sendall

✅ نصب دیتابیس با موفقیت تکمیل شد!
=====================================

📊 خلاصه عملیات:
• تعداد تیبل‌های پردازش شده: 11
• تیبل‌ها: apis, autoorders, category, manual, orders, payment, product, sendall, tusers, user, nowpayments
• ستون‌های بروزرسانی شده: payment_type به payment, datap به user

🎉 دیتابیس آماده استفاده است!
```

## نکات مهم

1. **قبل از اجرا حتماً بک‌آپ بگیرید** 🔒
2. فایل را بعد از نصب حذف کنید (امنیت)
3. تنظیمات دیتابیس را در جای امن نگه دارید
4. در محیط production حتماً رمز عبور تنظیم کنید

## پشتیبانی

در صورت بروز مشکل:
1. خطاها را با دقت بخوانید
2. تنظیمات دیتابیس را دوباره بررسی کنید
3. دسترسی‌های کاربر MySQL را چک کنید
4. نسخه PHP (حداقل 7.0) و MySQL (حداقل 5.7) را بررسی کنید
